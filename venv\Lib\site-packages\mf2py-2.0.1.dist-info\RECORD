mf2py-2.0.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
mf2py-2.0.1.dist-info/LICENSE,sha256=9nBOfDn-gaxCbnX_yXeRzt7-oi8fEQTRqxc7HaF-wCg,1076
mf2py-2.0.1.dist-info/METADATA,sha256=gaP0jNgNe_EVhOz5KUK73tyHMWWVfBVKclk1Z251nQY,5353
mf2py-2.0.1.dist-info/RECORD,,
mf2py-2.0.1.dist-info/WHEEL,sha256=vVCvjcmxuUltf8cYhJ0sJMRDLr1XsPuxEId8YDzbyCY,88
mf2py/__init__.py,sha256=v_IKAprspg94wIdv4xgjppTGfrGk7OXpM_7iNNoF34c,368
mf2py/__pycache__/__init__.cpython-311.pyc,,
mf2py/__pycache__/backcompat.cpython-311.pyc,,
mf2py/__pycache__/datetime_helpers.cpython-311.pyc,,
mf2py/__pycache__/dom_helpers.cpython-311.pyc,,
mf2py/__pycache__/implied_properties.cpython-311.pyc,,
mf2py/__pycache__/metaformats.cpython-311.pyc,,
mf2py/__pycache__/mf2_classes.cpython-311.pyc,,
mf2py/__pycache__/mf_helpers.cpython-311.pyc,,
mf2py/__pycache__/parse_property.cpython-311.pyc,,
mf2py/__pycache__/parser.cpython-311.pyc,,
mf2py/__pycache__/temp_fixes.cpython-311.pyc,,
mf2py/__pycache__/value_class_pattern.cpython-311.pyc,,
mf2py/__pycache__/version.cpython-311.pyc,,
mf2py/backcompat-rules/adr.json,sha256=qGJ98FV8M3C0RGLzpvvn4ta66XJ1QadW7bdrsTKnC-k,530
mf2py/backcompat-rules/geo.json,sha256=UOadNRm9abyZwLdFZ9OIe_F0UqRU12QTNUqUejA5Nbk,185
mf2py/backcompat-rules/hentry.json,sha256=ZTeE8JpFf5rf5oVbR2IHeNFJfhGftWd0dCLzX0_rFU8,705
mf2py/backcompat-rules/hfeed.json,sha256=HNYTyggT7tz2q8b2e3WcDJ5wMpV2BfxWjTKrpgwHqLU,435
mf2py/backcompat-rules/hproduct.json,sha256=Tya6DxQbNWy9UY_7C3LOUratH4ox3ugoCW9vozG88DY,595
mf2py/backcompat-rules/hrecipe.json,sha256=6bm25MP6aRhmxAiSSLLiwUi0nMzOPxKKt80qZfdbPb0,741
mf2py/backcompat-rules/hresume.json,sha256=TDy6tDi5RNtrmPL5vKNm8zvUa849_Z38D-sIn5vqrro,518
mf2py/backcompat-rules/hreview-aggregate.json,sha256=c9JucgHf7hTGNq5TR4cPp5eFB87ZWfyRTvK4_UMdHXg,828
mf2py/backcompat-rules/hreview.json,sha256=t9r-1l_9QGSGe-X8Dy2RdWB1OPrtOVMCgfISqoQ5Pug,1148
mf2py/backcompat-rules/vcard.json,sha256=7vmII0O01DFxsE4yf0mDFbSWMJ3fngq1jqTs6ysmo-w,2057
mf2py/backcompat-rules/vevent.json,sha256=RRq7aYzGrpu6JyQDug2HQM38aDnEMNVHYBkfbJRjDPc,758
mf2py/backcompat.py,sha256=gjR1MgaEzKs-JGLUlWKzweZqn1eJ3VOui1ZVH610azQ,6215
mf2py/datetime_helpers.py,sha256=iZPUcLz98zu6wdssrPT_PImjusIvWsJa59bcTW1eL40,1953
mf2py/dom_helpers.py,sha256=DIrazlXjf84YvI9_DXMKjayYyvJolL27hHHffvmvVBA,6502
mf2py/implied_properties.py,sha256=qOns2O9Bq2KOH4DFlzUpqovVhkyVPCdtgeN4bnDovto,6957
mf2py/metaformats.py,sha256=eM2Nix4-stSSPhDOE8rsuYgpMDdgr1d6U4M64l8WLFo,2605
mf2py/mf2_classes.py,sha256=RB_BseKmRvRphcsgjuOmXpuTT7FgAaAO-ficQ98eaBU,1139
mf2py/mf_helpers.py,sha256=YdQDji2Xmrfd2xK4IbVkWwSqMDhvXda_H96l62O88fo,939
mf2py/parse_property.py,sha256=yDmEt0cv4UeDJO_WB5q1G72JqsdR-GoKcM8m2KyKvI8,3762
mf2py/parser.py,sha256=QcGTlngZC8y1UchgHtvjwWbkMg68SAFUqttLOpD8iCs,23746
mf2py/temp_fixes.py,sha256=sGzjxZisNlOtHVZpG3HZTR6i2osvmF-u-LxOQSJgHgk,84
mf2py/value_class_pattern.py,sha256=XnpJQwoej7vzYnz6onri2XrkcpEZUrngk2H8_arpyHM,3061
mf2py/version.py,sha256=vt0kz2g_H05rIoiohCGDVF5KZ7OESzKORLUqaMvkwlk,296
