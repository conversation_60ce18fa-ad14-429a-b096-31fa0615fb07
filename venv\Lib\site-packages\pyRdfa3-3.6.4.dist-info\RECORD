pyRdfa/__init__.py,sha256=deE43XfjQL6K0PgnrZMUNii_WgMDMOoNsrW0R9CmyzI,52059
pyRdfa/__pycache__/__init__.cpython-311.pyc,,
pyRdfa/__pycache__/embeddedRDF.cpython-311.pyc,,
pyRdfa/__pycache__/initialcontext.cpython-311.pyc,,
pyRdfa/__pycache__/options.cpython-311.pyc,,
pyRdfa/__pycache__/parse.cpython-311.pyc,,
pyRdfa/__pycache__/property.cpython-311.pyc,,
pyRdfa/__pycache__/rdflibparsers.cpython-311.pyc,,
pyRdfa/__pycache__/state.cpython-311.pyc,,
pyRdfa/__pycache__/termorcurie.cpython-311.pyc,,
pyRdfa/__pycache__/utils.cpython-311.pyc,,
pyRdfa/embeddedRDF.py,sha256=y7fVYh1ZoRu1nfFPXGRIE2k7L6Nzo1qZEpd7PDAipxY,3907
pyRdfa/extras/__init__.py,sha256=ewApVDdXUYUQJiq-fpZBZ0ZO6OCIifz4kThymF8sCKs,105
pyRdfa/extras/__pycache__/__init__.cpython-311.pyc,,
pyRdfa/extras/__pycache__/httpheader.cpython-311.pyc,,
pyRdfa/extras/httpheader.py,sha256=D8DzCGmv6wEufJs1yg1d7ubDY2NY7rxF4XIeaUN8_g8,70183
pyRdfa/host/__init__.py,sha256=M0YxNcG3LGmj6aUf8QyCbbFi61UddyitcqYV9BmwvZY,10493
pyRdfa/host/__pycache__/__init__.cpython-311.pyc,,
pyRdfa/host/__pycache__/atom.cpython-311.pyc,,
pyRdfa/host/__pycache__/html5.cpython-311.pyc,,
pyRdfa/host/atom.py,sha256=eO3kHFL3sIELeMp1EGZGbhhY9D45R9WfoFlyEQvBg0w,1141
pyRdfa/host/html5.py,sha256=aso2SDUvcOaCEM1wK1n-TRZB0YvwKx5jscbYKv1a94A,8241
pyRdfa/initialcontext.py,sha256=G_nxFy2vLLfpl9A5wz0ghaoFHQe_ZyCiHEUt-X7pFv0,5454
pyRdfa/options.py,sha256=H21OTpqz9SdzV9dpuEwMInQ_mbcRn4fBACMwfpRHZQ8,11576
pyRdfa/parse.py,sha256=BZLHBMO6DjfECbF285eE1VkTAmSI7s4IedUTvKUA0uk,24123
pyRdfa/property.py,sha256=C-yoWXddKO_qNfAuLQASk_dnSokG7K6vDR-LKHXyVQE,13507
pyRdfa/rdflibparsers.py,sha256=hCCVUZv-uElooLgFqzalxWHuccSamjctNIpV_ttAPgg,14988
pyRdfa/rdfs/__init__.py,sha256=n0Jf0XpNT2zCojrNxPYRVzTaHmLgFEF07W_kAh0bXL8,1583
pyRdfa/rdfs/__pycache__/__init__.cpython-311.pyc,,
pyRdfa/rdfs/__pycache__/cache.cpython-311.pyc,,
pyRdfa/rdfs/__pycache__/process.cpython-311.pyc,,
pyRdfa/rdfs/cache.py,sha256=uKDorgeqrDe-s5ry0o-3YhpWxPIz4ur8L8yIEbglvX8,17240
pyRdfa/rdfs/process.py,sha256=S-1At7hwJi_FSQtpwDkXYQ60pUBv8Nj70j9n2KBRa_Q,12819
pyRdfa/state.py,sha256=gbD41iGy-zJ7edkLmYFAPCAwC0VeUez38eXGF-RIQoM,24502
pyRdfa/termorcurie.py,sha256=M0ocu76xb1fTG6xGR5GAlfmK5aulVzBViBIIUUUzCBc,23387
pyRdfa/transform/DublinCore.py,sha256=OWCojwvFiqPSYz6n5Mt_ic5ZSSe3ycS5buatl8pfa3o,3930
pyRdfa/transform/OpenID.py,sha256=TowN_EUxitKHp3IudntLWpxNy35k6LDhMP8VoYzlDHQ,2308
pyRdfa/transform/__init__.py,sha256=rdLhZQSfv5K8zt4km8p1x7WIrjfcgJ6dqyGgIabgVRc,4950
pyRdfa/transform/__pycache__/DublinCore.cpython-311.pyc,,
pyRdfa/transform/__pycache__/OpenID.cpython-311.pyc,,
pyRdfa/transform/__pycache__/__init__.cpython-311.pyc,,
pyRdfa/transform/__pycache__/lite.cpython-311.pyc,,
pyRdfa/transform/__pycache__/metaname.cpython-311.pyc,,
pyRdfa/transform/__pycache__/prototype.cpython-311.pyc,,
pyRdfa/transform/lite.py,sha256=0dI11FWNOJo7gYFwEDNuegXg9UZexuA5IoRCmXw2K1s,3478
pyRdfa/transform/metaname.py,sha256=3zB5M3jSyyQx1D3n_AUdVsFyjYShy938PzKVOFR3rIM,1153
pyRdfa/transform/prototype.py,sha256=eH8Bub1iIZ1SUu9FsskHmKtGKDJy0wWq3B9WhxqIevY,1328
pyRdfa/utils.py,sha256=apkz3UBF9zyObsnIadl2i6sm6ORzZA7xnuBSCCQBcPc,10229
pyRdfa3-3.6.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyRdfa3-3.6.4.dist-info/LICENSE,sha256=GOjCqSTUS9_c4wVdtwvU3n-tfx1g3ylFh1WmS8sEdm8,1901
pyRdfa3-3.6.4.dist-info/METADATA,sha256=dSqs6Fbnl9oggoWS5UVWCgxpGwEWe2UTh2K4FMt7IXk,3369
pyRdfa3-3.6.4.dist-info/RECORD,,
pyRdfa3-3.6.4.dist-info/WHEEL,sha256=Wyh-_nZ0DJYolHNn1_hMa4lM7uDedD_RGVwbmTjyItk,91
pyRdfa3-3.6.4.dist-info/top_level.txt,sha256=NhGN9_N-teibyYvfbpX1aDUW7al6WiIPLTdEjpWUII4,7
