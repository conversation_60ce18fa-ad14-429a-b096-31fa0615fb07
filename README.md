# Songkick Event Scraper

A powerful web scraper for collecting concert and event data from Songkick website. Uses JSON-LD structured data extraction for high-quality event information.

## Features

- **Multiple Event Types**: Scrape concerts, festivals, and music events
- **Location-based Scraping**: Collect events from different cities and countries
- **Detailed Information**: Extract event names, dates, locations, performers, and venue details
- **JSON Output**: Save data in clean JSON format
- **Pagination Support**: Collect data from multiple pages
- **Proxy Support**: Use mobile proxy for scraping

## Setup

1. Install dependencies:
```bash
pip install tls-client extruct rich
```

2. Set proxy environment variable (optional):
```bash
set mobileproxyuk=your_proxy_url
```

## Usage

### Basic Usage

```python
python main.py
```

### Configuration Options

Change the `EVENT_TYPE` variable in `main.py` to scrape different types of events:

```python
EVENT_TYPE = "us_concerts"    # US concerts (San Francisco Bay Area)
EVENT_TYPE = "uk_concerts"    # UK concerts (London)
EVENT_TYPE = "us_festivals"   # US festivals
EVENT_TYPE = "global_concerts" # Global concerts
```

### Multiple Pages Scraping

To collect data from multiple pages, uncomment this line in the code:

```python
events = scrape_multiple_pages(url, max_pages=3)
```

## Output Files

After running the script, two JSON files will be generated:

1. **`songkick_{EVENT_TYPE}_data.json`**: Raw JSON-LD data
2. **`songkick_{EVENT_TYPE}_detailed_data.json`**: Processed and cleaned data

## Data Structure

### Detailed Data Format

```json
{
  "name": "Event Name @ Venue",
  "type": "MusicEvent",
  "url": "https://www.songkick.com/...",
  "start_date": "2025-07-11",
  "end_date": "2025-07-12",
  "description": "Event description",
  "image": "https://images.sk-static.com/...",
  "location": {
    "name": "Venue Name",
    "address": {...},
    "city": "City Name",
    "country": "Country Code",
    "region": "State/Region",
    "postal_code": "Postal Code",
    "street_address": "Street Address"
  },
  "performers": ["Artist 1", "Artist 2", ...],
  "ticket_info": {
    "price": "Price",
    "currency": "USD",
    "availability": "InStock",
    "url": "Ticket URL"
  }
}
```

## Available URLs

```python
URLS = {
    "us_concerts": "https://www.songkick.com/metro-areas/26330-us-new-york",
    "uk_concerts": "https://www.songkick.com/metro-areas/24426-uk-london",
    "us_festivals": "https://www.songkick.com/festivals/countries/us",
    "global_concerts": "https://www.songkick.com/metro-areas/nearby"
}
```

## Features Explained

### 1. TLS Client
- Uses Chrome 105 browser fingerprinting
- Bypasses anti-bot detection

### 2. JSON-LD Extraction
- Extracts clean information from structured data
- Follows Schema.org format

### 3. Error Handling
- Handles HTTP errors
- Supports redirects
- Checks for empty data

### 4. Logging
- Beautiful console output using Rich library
- Progress tracking and debugging information

## Example Output

When you run the script, you'll see output like this:

```
INFO     Scraping us_concerts from: https://www.songkick.com/metro-areas/26330-us-new-york
INFO     Successfully fetched page. Status: 200
INFO     Found 51 JSON-LD objects
INFO     Processing JSON-LD object 1: Type = MusicEvent
INFO     Added single event: Wobbleland 2025 @ Cow Palace
...
INFO     Total events extracted: 50
INFO     Data saved to songkick_us_concerts_data.json
INFO     Sample of extracted events:
INFO     Event 1:
INFO       Name: Wobbleland 2025 @ Cow Palace
INFO       Type: MusicEvent
INFO       URL: https://www.songkick.com/festivals/503619-wobbleland/...
INFO       Date: 2025-07-11
INFO       Location: Cow Palace
```

## Customization

### Add New Cities/Regions

To add new cities or regions, add new entries to the `URLS` dictionary:

```python
URLS = {
    # Existing URLs...
    "nyc_concerts": "https://www.songkick.com/metro-areas/24426-us-new-york",
    "la_concerts": "https://www.songkick.com/metro-areas/17835-us-los-angeles",
}
```

### Filter by Date/Genre

You can filter by adding query parameters to Songkick URLs:

```python
url = "https://www.songkick.com/metro-areas/26330-us-new-york?filters[minDate]=2025-07-01&filters[maxDate]=2025-12-31"
```

## Notes

- **Respectful scraping**: Add delays between requests
- **Use proxy**: Avoid rate limiting
- **Error handling**: Implement proper error handling for large-scale scraping
- **Terms of service**: Respect Songkick's robots.txt and terms of service

## Troubleshooting

### Common Issues

1. **Status Code 301/302**: URL redirects - script handles automatically
2. **No Events Found**: Page structure changed or wrong URL
3. **Proxy Issues**: Ensure environment variable is properly set
4. **Rate Limiting**: Add delays between requests

### Debug Mode

For more detailed logging:

```python
logging.basicConfig(level="DEBUG")
```
