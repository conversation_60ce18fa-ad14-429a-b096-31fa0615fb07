# Songkick Event Scraper

এই scraper Songkick website থেকে concerts এবং events এর data collect করে। এটি JSON-LD structured data ব্যবহার করে high-quality event information extract করে।

## Features

- **Multiple Event Types**: Concerts, festivals, এবং music events scrape করতে পারে
- **Location-based Scraping**: বিভিন্ন শহর/দেশের events collect করা যায়
- **Detailed Information**: Event name, date, location, performers, venue details সব পাওয়া যায়
- **JSON Output**: Clean JSON format এ data save হয়
- **Pagination Support**: Multiple pages থেকে data collect করা যায়
- **Proxy Support**: Mobile proxy দিয়ে scraping করা যায়

## Setup

1. Install dependencies:
```bash
pip install tls-client extruct rich
```

2. Set proxy environment variable (optional):
```bash
set mobileproxyuk=your_proxy_url
```

## Usage

### Basic Usage

```python
python main.py
```

### Configuration Options

`main.py` file এ `EVENT_TYPE` variable change করে বিভিন্ন ধরনের events scrape করতে পারেন:

```python
EVENT_TYPE = "us_concerts"    # US concerts (San Francisco Bay Area)
EVENT_TYPE = "uk_concerts"    # UK concerts (London)
EVENT_TYPE = "us_festivals"   # US festivals
EVENT_TYPE = "global_concerts" # Global concerts
```

### Multiple Pages Scraping

Multiple pages থেকে data collect করতে চাইলে code এ এই line uncomment করুন:

```python
events = scrape_multiple_pages(url, max_pages=3)
```

## Output Files

Script run করার পর দুইটি JSON file তৈরি হবে:

1. **`songkick_{EVENT_TYPE}_data.json`**: Raw JSON-LD data
2. **`songkick_{EVENT_TYPE}_detailed_data.json`**: Processed এবং cleaned data

## Data Structure

### Detailed Data Format

```json
{
  "name": "Event Name @ Venue",
  "type": "MusicEvent",
  "url": "https://www.songkick.com/...",
  "start_date": "2025-07-11",
  "end_date": "2025-07-12",
  "description": "Event description",
  "image": "https://images.sk-static.com/...",
  "location": {
    "name": "Venue Name",
    "address": {...},
    "city": "City Name",
    "country": "Country Code",
    "region": "State/Region",
    "postal_code": "Postal Code",
    "street_address": "Street Address"
  },
  "performers": ["Artist 1", "Artist 2", ...],
  "ticket_info": {
    "price": "Price",
    "currency": "USD",
    "availability": "InStock",
    "url": "Ticket URL"
  }
}
```

## Available URLs

```python
URLS = {
    "us_concerts": "https://www.songkick.com/metro-areas/26330-us-sf-bay-area",
    "uk_concerts": "https://www.songkick.com/metro-areas/24426-uk-london",
    "us_festivals": "https://www.songkick.com/festivals/countries/us",
    "global_concerts": "https://www.songkick.com/metro-areas/nearby"
}
```

## Features Explained

### 1. TLS Client
- Chrome 105 browser fingerprinting ব্যবহার করে
- Anti-bot detection bypass করে

### 2. JSON-LD Extraction
- Structured data থেকে clean information extract করে
- Schema.org format follow করে

### 3. Error Handling
- HTTP errors handle করে
- Redirect support আছে
- Empty data check করে

### 4. Logging
- Rich library দিয়ে beautiful console output
- Progress tracking এবং debugging info

## Example Output

Script run করলে এরকম output দেখবেন:

```
INFO     Scraping us_concerts from: https://www.songkick.com/metro-areas/26330-us-sf-bay-area
INFO     Response status: 200
INFO     Successfully fetched page. Status: 200
INFO     Found 51 JSON-LD objects
INFO     Processing JSON-LD object 1: Type = MusicEvent
INFO     Added single event: Wobbleland 2025 @ Cow Palace
...
INFO     Total events extracted: 50
INFO     Data saved to songkick_us_concerts_data.json
INFO     Sample of extracted events:
INFO     Event 1:
INFO       Name: Wobbleland 2025 @ Cow Palace
INFO       Type: MusicEvent
INFO       URL: https://www.songkick.com/festivals/503619-wobbleland/...
INFO       Date: 2025-07-11
INFO       Location: Cow Palace
```

## Customization

### Add New Cities/Regions

নতুন city বা region add করতে `URLS` dictionary তে নতুন entry add করুন:

```python
URLS = {
    # Existing URLs...
    "nyc_concerts": "https://www.songkick.com/metro-areas/24426-us-new-york",
    "la_concerts": "https://www.songkick.com/metro-areas/17835-us-los-angeles",
}
```

### Filter by Date/Genre

Songkick URLs এ query parameters add করে filter করা যায়:

```python
url = "https://www.songkick.com/metro-areas/26330-us-sf-bay-area?filters[minDate]=2025-07-01&filters[maxDate]=2025-12-31"
```

## Notes

- Respectful scraping: Request গুলোর মধ্যে delay রাখুন
- Proxy ব্যবহার করুন rate limiting avoid করতে
- Large scale scraping এর জন্য proper error handling implement করুন
- Songkick এর robots.txt এবং terms of service respect করুন

## Troubleshooting

### Common Issues

1. **Status Code 301/302**: URL redirect হচ্ছে - script automatically handle করে
2. **No Events Found**: Page structure change হয়েছে বা wrong URL
3. **Proxy Issues**: Environment variable properly set করুন
4. **Rate Limiting**: Delay add করুন requests এর মধ্যে

### Debug Mode

More detailed logging এর জন্য:

```python
logging.basicConfig(level="DEBUG")
```
