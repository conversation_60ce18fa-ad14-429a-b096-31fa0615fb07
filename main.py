import tls_client
import os
from extruct.jsonld import JsonLdExtractor
import logging
from rich.logging import RichHandler
import json

FORMAT = "%(message)s"
logging.basicConfig(
    level="NOTSET", format=FORMAT, datefmt="[%X]", handlers=[RichHandler()]
)
log = logging.getLogger("rich")

jsde = JsonLdExtractor()

session = tls_client.Session(
    client_identifier="chrome_105",
    random_tls_extension_order=True,
)

# Different URLs for different types of events
URLS = {
    "us_concerts": "https://www.songkick.com/metro-areas/26330-us-new-york",  # New York concerts (corrected ID)
    "uk_concerts": "https://www.songkick.com/metro-areas/24426-uk-london",   # London concerts
    "us_festivals": "https://www.songkick.com/festivals/countries/us",       # US festivals
    "global_concerts": "https://www.songkick.com/metro-areas/nearby"         # Global concerts
}

# Choose which type of events to scrape
EVENT_TYPE = "us_concerts"  # Change this to scrape different types
url = URLS[EVENT_TYPE]

session.proxies.update(
    { "http": os.getenv("mobileproxyuk"), "https": os.getenv("mobileproxyuk") }
)

session.headers.update(
    {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "en-US,en;q=8.5",
        "Dnt": "1",
        "Priority": "u=1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
)

log.info(f"Scraping {EVENT_TYPE} from: {url}")
response = session.get(url, allow_redirects=True)

log.info(f"Response status: {response.status_code}")
log.info(f"Final URL after redirects: {response.url}")

if response.status_code not in [200, 301, 302]:
    log.error(f"Failed to fetch data. Status code: {response.status_code}")
    exit(1)

log.info(f"Successfully fetched page. Status: {response.status_code}")

# Extract JSON-LD data
data = jsde.extract(response.text)
log.info(f"Found {len(data)} JSON-LD objects")

events = []

# Process different types of structured data
for idx, item in enumerate(data):
    log.info(f"Processing JSON-LD object {idx + 1}: Type = {item.get('@type', 'Unknown')}")

    # Handle different schema types
    if item.get("@type") == "ItemList" and "itemListElement" in item:
        # This is likely a list of events/festivals
        for element in item["itemListElement"]:
            events.append(element)
            log.info(f"Added item from ItemList: {element.get('name', 'Unknown')}")

    elif item.get("@type") in ["Event", "MusicEvent", "Concert"]:
        # This is a single event
        events.append(item)
        log.info(f"Added single event: {item.get('name', 'Unknown')}")

    elif item.get("@type") == "WebPage" and "mainEntity" in item:
        # Sometimes events are nested in WebPage mainEntity
        main_entity = item["mainEntity"]
        if isinstance(main_entity, list):
            for entity in main_entity:
                if entity.get("@type") in ["Event", "MusicEvent", "Concert"]:
                    events.append(entity)
                    log.info(f"Added event from WebPage: {entity.get('name', 'Unknown')}")
        elif main_entity.get("@type") in ["Event", "MusicEvent", "Concert"]:
            events.append(main_entity)
            log.info(f"Added event from WebPage: {main_entity.get('name', 'Unknown')}")

log.info(f"Total events extracted: {len(events)}")

# Function to scrape multiple pages (for pagination)
def scrape_multiple_pages(base_url, max_pages=5):
    """Scrape multiple pages of events"""
    all_events = []

    for page in range(1, max_pages + 1):
        try:
            page_url = f"{base_url}?page={page}" if page > 1 else base_url
            log.info(f"Scraping page {page}: {page_url}")

            response = session.get(page_url)
            if response.status_code != 200:
                log.warning(f"Failed to fetch page {page}. Status: {response.status_code}")
                break

            page_data = jsde.extract(response.text)
            page_events = []

            for item in page_data:
                if item.get("@type") == "ItemList" and "itemListElement" in item:
                    for element in item["itemListElement"]:
                        page_events.append(element)
                elif item.get("@type") in ["Event", "MusicEvent", "Concert"]:
                    page_events.append(item)

            if not page_events:
                log.info(f"No events found on page {page}. Stopping pagination.")
                break

            all_events.extend(page_events)
            log.info(f"Found {len(page_events)} events on page {page}")

            # Add small delay to be respectful
            import time
            time.sleep(1)

        except Exception as e:
            log.error(f"Error scraping page {page}: {e}")
            break

    return all_events

# Uncomment the line below to scrape multiple pages
# events = scrape_multiple_pages(url, max_pages=3)

# Save the extracted data
output_file = f"songkick_{EVENT_TYPE}_data.json"
with open(output_file, 'w', encoding='utf-8') as f:
    json.dump(events, f, indent=2, ensure_ascii=False)

log.info(f"Data saved to {output_file}")

# Display sample of extracted data
if events:
    log.info("Sample of extracted events:")
    for i, event in enumerate(events[:3]):  # Show first 3 events
        log.info(f"Event {i+1}:")
        log.info(f"  Name: {event.get('name', 'N/A')}")
        log.info(f"  Type: {event.get('@type', 'N/A')}")
        log.info(f"  URL: {event.get('url', 'N/A')}")
        if 'startDate' in event:
            log.info(f"  Date: {event.get('startDate', 'N/A')}")
        if 'location' in event:
            location = event['location']
            if isinstance(location, dict):
                log.info(f"  Location: {location.get('name', 'N/A')}")
        log.info("  ---")
else:
    log.warning("No events found. The page structure might have changed.")

# Additional function to extract more detailed event information
def extract_detailed_event_info(events):
    """Extract detailed information from events"""
    detailed_events = []

    for event in events:
        detailed_event = {
            'name': event.get('name', 'N/A'),
            'type': event.get('@type', 'N/A'),
            'url': event.get('url', 'N/A'),
            'start_date': event.get('startDate', 'N/A'),
            'end_date': event.get('endDate', 'N/A'),
            'description': event.get('description', 'N/A'),
            'image': event.get('image', 'N/A'),
        }

        # Extract location information
        if 'location' in event:
            location = event['location']
            if isinstance(location, dict):
                detailed_event['location'] = {
                    'name': location.get('name', 'N/A'),
                    'address': location.get('address', 'N/A'),
                    'city': location.get('addressLocality', 'N/A'),
                    'country': location.get('addressCountry', 'N/A')
                }

        # Extract performer information
        if 'performer' in event:
            performers = event['performer']
            if isinstance(performers, list):
                detailed_event['performers'] = [p.get('name', 'N/A') for p in performers]
            elif isinstance(performers, dict):
                detailed_event['performers'] = [performers.get('name', 'N/A')]

        # Extract offers/ticket information
        if 'offers' in event:
            offers = event['offers']
            if isinstance(offers, dict):
                detailed_event['ticket_info'] = {
                    'price': offers.get('price', 'N/A'),
                    'currency': offers.get('priceCurrency', 'N/A'),
                    'availability': offers.get('availability', 'N/A'),
                    'url': offers.get('url', 'N/A')
                }

        detailed_events.append(detailed_event)

    return detailed_events

# Extract detailed information and save
detailed_events = extract_detailed_event_info(events)
detailed_output_file = f"songkick_{EVENT_TYPE}_detailed_data.json"
with open(detailed_output_file, 'w', encoding='utf-8') as f:
    json.dump(detailed_events, f, indent=2, ensure_ascii=False)

log.info(f"Detailed data saved to {detailed_output_file}")
log.info(f"Total events processed: {len(detailed_events)}")
     
